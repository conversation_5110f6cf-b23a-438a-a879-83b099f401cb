plugins {
    alias(libs.plugins.android.application)
    alias(libs.plugins.kotlin.android)
}

android {
    namespace 'com.seres.background.upgrade'
    compileSdk 34

    defaultConfig {
        applicationId "com.seres.background.upgrade"
        minSdk 31
        targetSdk 34
        versionCode 1
        versionName "1.2.4"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    sourceSets {
        main {
            java {
                // 暂时排除有问题的RPC文件
                exclude '**/idlGen/rpc/**'
            }
        }
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = '1.8'
    }

    buildFeatures {
        aidl false
    }

    lint {
        abortOnError false
    }
}

dependencies {
    implementation libs.androidx.core.ktx
    implementation libs.androidx.appcompat
    implementation 'com.google.code.gson:gson:2.10.1'
    implementation 'com.seres.s2s:sdk-api:1.0.0-SNAPSHOT'
    implementation project(":nativelib")
    testImplementation libs.junit
    androidTestImplementation libs.androidx.junit
    androidTestImplementation libs.androidx.espresso.core
}
