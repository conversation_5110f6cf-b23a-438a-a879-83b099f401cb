package Seres.OTAM_UDiskSrv


import com.seres.dds.sdk.*
import com.seres.dds.sdk.idl.*


class OTAM_UDiskSrv_Status : TypeStruct() {
    private var _inventory_info_status : Seres.OTAM_UDiskSrv.InventoryInfoStatus? = null

    init{
        keyless = true
        version_support = 1
        typename = "Seres::OTAM_UDiskSrv::OTAM_UDiskSrv_Status"
        orderedMembers = arrayListOf(
            Member("_inventory_info_status", true),
        )

        initproperty()
    }

    var inventory_info_status: Seres.OTAM_UDiskSrv.InventoryInfoStatus?
        get() = _inventory_info_status
        set(value){
            _inventory_info_status = value
        }

    fun copy(value: OTAM_UDiskSrv_Status = this): OTAM_UDiskSrv_Status{
        this._inventory_info_status =  value._inventory_info_status
        return this
    }

    override fun toString(): String{
        return "$typename(inventory_info_status=$inventory_info_status)"
    }
}

