package Seres.UDiskSrv_OTAM


import com.seres.dds.sdk.idl.*

open class UDiskSrv_OTAM_Control : TypeUnion(){

    protected var __d: Int = 0
    protected var __u: Any? = null

    init{
        keyless = true
        version_support = 1
        typename = "Seres::UDiskSrv_OTAM::UDiskSrv_OTAM_Control"
        dmutableMap.put(1892738123, descriptor_1892738123::class)
        dmutableMap.put(374875982, descriptor_374875982::class)
    }

    var _d : Int
        get() = __d
        set(value) {
            __d = value
        }

    class descriptor_1892738123() : UDiskSrv_OTAM_Control(){
        private var _upgrade_task_info: Seres.UDiskSrv_OTAM.UpgradeTaskInfo = ""

        init{
            _d = 1892738123
            orderedMembers = arrayListOf(
                Member("__d", false),
                Member("_upgrade_task_info", false))

            __u = this

            initproperty()
        }

        var upgrade_task_info: Seres.UDiskSrv_OTAM.UpgradeTaskInfo
            get() = _upgrade_task_info
            set(value) {
                _upgrade_task_info = value
            }

        override fun toString(): String {
            return "$typename(_d = $_d, upgrade_task_info=$upgrade_task_info)"
        }

    }

    class descriptor_374875982() : UDiskSrv_OTAM_Control(){
        private var _upgrade_task_is_match: Seres.UDiskSrv_OTAM.UpgradeTaskIsMatch = false

        init{
            _d = 374875982
            orderedMembers = arrayListOf(
                Member("__d", false),
                Member("_upgrade_task_is_match", false))

            __u = this

            initproperty()
        }

        var upgrade_task_is_match: Seres.UDiskSrv_OTAM.UpgradeTaskIsMatch
            get() = _upgrade_task_is_match
            set(value) {
                _upgrade_task_is_match = value
            }

        override fun toString(): String {
            return "$typename(_d = $_d, upgrade_task_is_match=$upgrade_task_is_match)"
        }

    }

    var _u_descriptor_1892738123: descriptor_1892738123
        get(){
            __u?.let{
                if ((__u as descriptor_1892738123)._d == 1892738123){
                    return __u!! as descriptor_1892738123
                }else{
                    throw IllegalArgumentException("Error: union _d not match")
                }
            }?: throw IllegalArgumentException("Error: _u is not set")
        }

        set(@Suppress("UNUSED_PARAMETER") value){
            throw IllegalArgumentException("Error: can not set value to _u")
        }

    var _u_descriptor_374875982: descriptor_374875982
        get(){
            __u?.let{
                if ((__u as descriptor_374875982)._d == 374875982){
                    return __u!! as descriptor_374875982
                }else{
                    throw IllegalArgumentException("Error: union _d not match")
                }
            }?: throw IllegalArgumentException("Error: _u is not set")
        }

        set(@Suppress("UNUSED_PARAMETER") value){
            throw IllegalArgumentException("Error: can not set value to _u")
        }


}



