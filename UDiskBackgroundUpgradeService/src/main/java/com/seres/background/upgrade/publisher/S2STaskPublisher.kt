package com.seres.background.upgrade.publisher

import android.content.Context
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import com.seres.dds.server.CarControlManager
import com.seres.background.upgrade.model.InventoryInfo
import com.seres.background.upgrade.utils.LogUtils
import com.google.gson.Gson
import java.util.concurrent.Executors

/**
 * S2S任务发布者
 * 负责通过DDS库发布升级任务和接收资产信息
 */
class S2STaskPublisher(private val context: Context) {

    private val TAG = "S2STaskPublisher"
    private val threadPool = Executors.newCachedThreadPool()
    private val mainHandler = Handler(Looper.getMainLooper())
    private val gson = Gson()

    // DDS服务实例
    private val ddsService = DdsService()
    private var isInitialized = false

    // CarControlManager实例 (保留作为备用)
    private var carControlManager: CarControlManager? = null

    // 回调接口
    private var versionCompatibilityChecker: VersionCompatibilityChecker? = null

    // 升级任务相关常量
    companion object {
        const val UPGRADE_TASK_NOTIFY_HASH = -320322560 // UDiskSrv_UpgradeTaskNotify_OTAM_HASH
        const val UPGRADE_TASK_MATCH_HASH = 1137060134 // UDiskSrv_UpgradeTaskIsMatch_OTAM_HASH
        const val INVENTORY_INFO_SIGNAL_HASH = 1001 // 资产信息信号Hash (需要根据实际IDL确定)
        const val UPGRADE_APP_ID = 999 // 升级服务的App ID
    }

    /**
     * 版本兼容性检查器接口
     */
    interface VersionCompatibilityChecker {
        fun checkCompatibility(inventoryInfoList: List<InventoryInfo>, taskId: String): Boolean
    }



    init {
        LogUtils.i(TAG, "初始化S2STaskPublisher")
        initializeDdsService()
        LogUtils.i(TAG, "S2STaskPublisher初始化完成")
    }

    /**
     * 初始化DDS服务
     */
    private fun initializeDdsService() {
        try {
            // 初始化DDS服务
            isInitialized = ddsService.initialize()

            if (isInitialized) {
                // 设置资产信息回调
                ddsService.setInventoryInfoCallback(object : DdsService.InventoryInfoCallback {
                    override fun onInventoryInfoReceived(inventoryInfoStatus: String) {
                        handleInventoryInfoNotification(inventoryInfoStatus)
                    }
                })

                LogUtils.i(TAG, "DDS服务初始化成功")
            } else {
                LogUtils.e(TAG, "DDS服务初始化失败")

                // 延迟重试初始化
                mainHandler.postDelayed({
                    LogUtils.i(TAG, "重试初始化DDS服务")
                    initializeDdsService()
                }, 3000)
            }

        } catch (e: Exception) {
            LogUtils.e(TAG, "DDS服务初始化失败: ${e.message}", e)
            isInitialized = false

            // 延迟重试初始化
            mainHandler.postDelayed({
                LogUtils.i(TAG, "重试初始化DDS服务")
                initializeDdsService()
            }, 3000)
        }
    }

    /**
     * 设置版本兼容性检查器
     */
    fun setVersionCompatibilityChecker(checker: VersionCompatibilityChecker) {
        this.versionCompatibilityChecker = checker
    }



    /**
     * 发布升级任务
     */
    fun publishUpgradeTask(taskId: String, taskJson: String, callback: (Boolean) -> Unit) {
        threadPool.submit {
            try {
                LogUtils.i(TAG, "发布升级任务: $taskId")

                if (!isInitialized) {
                    LogUtils.w(TAG, "DDS服务未初始化，尝试重新初始化")
                    initializeDdsService()
                    Thread.sleep(2000) // 等待初始化
                }

                if (isInitialized) {
                    // 通过DDS服务发布升级任务
                    val success = ddsService.publishUpgradeTaskInfo(taskJson)

                    if (success) {
                        LogUtils.i(TAG, "升级任务发布成功: $taskId")
                        callback(true)
                    } else {
                        LogUtils.e(TAG, "升级任务发布失败: $taskId")
                        callback(false)
                    }
                } else {
                    LogUtils.e(TAG, "无法发布任务: DDS服务未初始化")
                    callback(false)
                }

            } catch (e: Exception) {
                LogUtils.e(TAG, "发布升级任务时出错: ${e.message}", e)
                callback(false)
            }
        }
    }

    /**
     * 通知升级任务匹配结果
     */
    fun notifyUpgradeTaskMatch(taskId: String, isMatch: Boolean) {
        threadPool.submit {
            try {
                LogUtils.i(TAG, "通知升级任务匹配结果: $taskId -> $isMatch")

                if (isInitialized) {
                    // 通过DDS服务发布匹配结果
                    val success = ddsService.publishUpgradeTaskMatch(isMatch)

                    if (success) {
                        LogUtils.i(TAG, "升级任务匹配通知发送成功: $taskId")
                    } else {
                        LogUtils.e(TAG, "升级任务匹配通知发送失败: $taskId")
                    }
                } else {
                    LogUtils.e(TAG, "无法发送匹配通知: DDS服务未初始化")
                }

            } catch (e: Exception) {
                LogUtils.e(TAG, "发送升级任务匹配通知时出错: ${e.message}", e)
            }
        }
    }

    /**
     * 处理资产信息通知
     */
    private fun handleInventoryInfoNotification(inventoryInfoStatus: String) {
        try {
            LogUtils.d(TAG, "收到资产信息通知: $inventoryInfoStatus")

            // 解析资产信息数据
            val inventoryInfoList = parseInventoryInfoFromJson(inventoryInfoStatus)

            if (inventoryInfoList.isNotEmpty()) {
                // 检查版本兼容性
                val isCompatible = versionCompatibilityChecker?.checkCompatibility(inventoryInfoList, "") ?: false

                // 通知升级任务匹配结果
                notifyUpgradeTaskMatch("", isCompatible)

                LogUtils.i(TAG, "版本兼容性检查完成: $isCompatible")
            } else {
                LogUtils.w(TAG, "资产信息数据不完整")
            }

        } catch (e: Exception) {
            LogUtils.e(TAG, "处理资产信息通知时出错: ${e.message}", e)
        }
    }

    /**
     * 从JSON字符串中解析资产信息
     */
    private fun parseInventoryInfoFromJson(inventoryInfoStatus: String): List<InventoryInfo> {
        val inventoryInfoList = mutableListOf<InventoryInfo>()

        try {
            // 尝试解析JSON格式的资产信息
            // 这里假设inventoryInfoStatus是一个JSON字符串，包含资产信息数组
            val inventoryArray = gson.fromJson(inventoryInfoStatus, Array<InventoryInfo>::class.java)

            inventoryArray?.let {
                inventoryInfoList.addAll(it)
            } ?: run {
                // 如果不是数组格式，尝试解析单个对象
                val singleInventory = gson.fromJson(inventoryInfoStatus, InventoryInfo::class.java)
                singleInventory?.let {
                    inventoryInfoList.add(it)
                }
            }

        } catch (e: Exception) {
            LogUtils.e(TAG, "解析资产信息JSON失败: ${e.message}", e)

            // 如果JSON解析失败，尝试简单的字符串解析
            try {
                val inventoryInfo = InventoryInfo(
                    ecuName = "Unknown",
                    softwareVersion = inventoryInfoStatus,
                    partNumber = "",
                    supplierCode = "",
                    serialNumber = "",
                    hardwareVersion = "",
                    bootloaderVersion = "",
                    backupVersion = ""
                )
                inventoryInfoList.add(inventoryInfo)
            } catch (e2: Exception) {
                LogUtils.e(TAG, "简单解析资产信息也失败: ${e2.message}", e2)
            }
        }

        return inventoryInfoList
    }

    /**
     * 检查DDS连接状态
     */
    fun isConnected(): Boolean = isInitialized && ddsService.isConnected()

    /**
     * 重新初始化DDS服务
     */
    fun reconnect() {
        if (!isInitialized) {
            LogUtils.i(TAG, "重新初始化DDS服务")
            initializeDdsService()
        }
    }

    /**
     * 清理资源
     */
    fun cleanup() {
        try {
            // 清理DDS服务
            ddsService.cleanup()
            isInitialized = false

            mainHandler.removeCallbacksAndMessages(null) // 清除所有未执行的重试任务
            threadPool.shutdown() // 关闭线程池
            LogUtils.i(TAG, "S2STaskPublisher清理完成")
        } catch (e: Exception) {
            LogUtils.e(TAG, "清理资源时出错: ${e.message}", e)
        }
    }
}
