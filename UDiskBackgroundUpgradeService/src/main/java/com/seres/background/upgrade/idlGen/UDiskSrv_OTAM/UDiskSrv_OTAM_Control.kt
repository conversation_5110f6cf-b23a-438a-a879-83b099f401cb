package com.seres.background.upgrade.idlGen.UDiskSrv_OTAM


import com.seres.dds.sdk.*
import com.seres.dds.sdk.idl.*


open class UDiskSrv_OTAM_Control : TypeUnion(){

    protected var __d: Int = 0
    protected var __u: Any? = null

    init{
        keyless = true
        version_support = 1
        typename = "Seres::UDiskSrv_OTAM::UDiskSrv_OTAM_Control"
        dmutableMap.put(com.seres.background.upgrade.idlGen.UDiskSrv_OTAM.UDiskSrvOtamCtrlType.UPGRADE_TASK_INFO, UpgradeTaskInfoDescriptor::class)
        dmutableMap.put(com.seres.background.upgrade.idlGen.UDiskSrv_OTAM.UDiskSrvOtamCtrlType.UPGRADE_TASK_IS_MATCH, UpgradeTaskIsMatchDescriptor::class)
    }

    var _d: com.seres.background.upgrade.idlGen.UDiskSrv_OTAM.UDiskSrvOtamCtrlType
        get() = com.seres.background.upgrade.idlGen.UDiskSrv_OTAM.UDiskSrvOtamCtrlType.values().first{ it.basevalue == __d }
        set(value) {
            __d = value.basevalue
        }

    class UpgradeTaskInfoDescriptor() : UDiskSrv_OTAM_Control(){
        private var _upgrade_task_info: com.seres.background.upgrade.idlGen.UDiskSrv_OTAM.UpgradeTaskInfo = ""

        init{
            _d = com.seres.background.upgrade.idlGen.UDiskSrv_OTAM.UDiskSrvOtamCtrlType.UPGRADE_TASK_INFO
            orderedMembers = arrayListOf(
                Member("__d", false),
                Member("_upgrade_task_info", false))

            __u = this

            initproperty()
        }

        var upgrade_task_info: com.seres.background.upgrade.idlGen.UDiskSrv_OTAM.UpgradeTaskInfo
        get() = _upgrade_task_info
        set(value) {
            _upgrade_task_info = value
        }

        override fun toString(): String {
            return "$typename(_d = $_d, upgrade_task_info=$upgrade_task_info)"
        }

    }

    class UpgradeTaskIsMatchDescriptor() : UDiskSrv_OTAM_Control(){
        private var _upgrade_task_is_match: com.seres.background.upgrade.idlGen.UDiskSrv_OTAM.UpgradeTaskIsMatch = false

        init{
            _d = com.seres.background.upgrade.idlGen.UDiskSrv_OTAM.UDiskSrvOtamCtrlType.UPGRADE_TASK_IS_MATCH
            orderedMembers = arrayListOf(
                Member("__d", false),
                Member("_upgrade_task_is_match", false))

            __u = this

            initproperty()
        }

        var upgrade_task_is_match: com.seres.background.upgrade.idlGen.UDiskSrv_OTAM.UpgradeTaskIsMatch
        get() = _upgrade_task_is_match
        set(value) {
            _upgrade_task_is_match = value
        }

        override fun toString(): String {
            return "$typename(_d = $_d, upgrade_task_is_match=$upgrade_task_is_match)"
        }

    }

    var upgradeTaskInfoDescriptor: UpgradeTaskInfoDescriptor
        get(){
            __u?.let{
                if ((__u as UpgradeTaskInfoDescriptor)._d == com.seres.background.upgrade.idlGen.UDiskSrv_OTAM.UDiskSrvOtamCtrlType.UPGRADE_TASK_INFO){
                    return __u!! as UpgradeTaskInfoDescriptor
                }else{
                    throw IllegalArgumentException("Error: union _d not match")
                }
            }?: throw IllegalArgumentException("Error: _u is not set")
        }

        set(@Suppress("UNUSED_PARAMETER") value){
            throw IllegalArgumentException("Error: can not set value to _u")
        }

    var upgradeTaskIsMatchDescriptor: UpgradeTaskIsMatchDescriptor
        get(){
            __u?.let{
                if ((__u as UpgradeTaskIsMatchDescriptor)._d == com.seres.background.upgrade.idlGen.UDiskSrv_OTAM.UDiskSrvOtamCtrlType.UPGRADE_TASK_IS_MATCH){
                    return __u!! as UpgradeTaskIsMatchDescriptor
                }else{
                    throw IllegalArgumentException("Error: union _d not match")
                }
            }?: throw IllegalArgumentException("Error: _u is not set")
        }

        set(@Suppress("UNUSED_PARAMETER") value){
            throw IllegalArgumentException("Error: can not set value to _u")
        }


}

