package com.seres.background.upgrade.idlGen.UDiskSrv_OTAM


import com.seres.dds.sdk.*
import com.seres.dds.sdk.idl.*


open class UDiskSrv_OTAM_Control : TypeUnion(){

    protected var __d: Int = 0
    protected var __u: Any? = null

    init{
        keyless = true
        version_support = 1
        typename = "Seres::UDiskSrv_OTAM::UDiskSrv_OTAM_Control"
        dmutableMap.put(com.seres.background.upgrade.idlGen.UDiskSrv_OTAM.UDiskSrvOtamCtrlType.UPGRADE_TASK_INFO, descriptor_Seres.UDiskSrv_OTAM.UDiskSrvOtamCtrlType.UPGRADE_TASK_INFO::class)
        dmutableMap.put(com.seres.background.upgrade.idlGen.UDiskSrv_OTAM.UDiskSrvOtamCtrlType.UPGRADE_TASK_IS_MATCH, descriptor_Seres.UDiskSrv_OTAM.UDiskSrvOtamCtrlType.UPGRADE_TASK_IS_MATCH::class)
    }

    var _d: com.seres.background.upgrade.idlGen.UDiskSrv_OTAM.UDiskSrvOtamCtrlType
        get() = com.seres.background.upgrade.idlGen.UDiskSrv_OTAM.UDiskSrvOtamCtrlType.values().first{ it.basevalue == __d }
        set(value) {
            __d = value.basevalue
        }

    class descriptor_Seres.UDiskSrv_OTAM.UDiskSrvOtamCtrlType.UPGRADE_TASK_INFO() : UDiskSrv_OTAM_Control(){
        private var _upgrade_task_info: com.seres.background.upgrade.idlGen.UDiskSrv_OTAM.UpgradeTaskInfo = ""

        init{
            _d = com.seres.background.upgrade.idlGen.UDiskSrv_OTAM.UDiskSrvOtamCtrlType.UPGRADE_TASK_INFO
            orderedMembers = arrayListOf(
                Member("__d", false),
                Member("_upgrade_task_info", false))

            __u = this

            initproperty()
        }

        var upgrade_task_info: com.seres.background.upgrade.idlGen.UDiskSrv_OTAM.UpgradeTaskInfo
        get() = _upgrade_task_info
        set(value) {
            _upgrade_task_info = value
        }

        override fun toString(): String {
            return "$typename(_d = $_d, upgrade_task_info=$upgrade_task_info)"
        }

    }

    class descriptor_Seres.UDiskSrv_OTAM.UDiskSrvOtamCtrlType.UPGRADE_TASK_IS_MATCH() : UDiskSrv_OTAM_Control(){
        private var _upgrade_task_is_match: com.seres.background.upgrade.idlGen.UDiskSrv_OTAM.UpgradeTaskIsMatch = false

        init{
            _d = com.seres.background.upgrade.idlGen.UDiskSrv_OTAM.UDiskSrvOtamCtrlType.UPGRADE_TASK_IS_MATCH
            orderedMembers = arrayListOf(
                Member("__d", false),
                Member("_upgrade_task_is_match", false))

            __u = this

            initproperty()
        }

        var upgrade_task_is_match: com.seres.background.upgrade.idlGen.UDiskSrv_OTAM.UpgradeTaskIsMatch
        get() = _upgrade_task_is_match
        set(value) {
            _upgrade_task_is_match = value
        }

        override fun toString(): String {
            return "$typename(_d = $_d, upgrade_task_is_match=$upgrade_task_is_match)"
        }

    }

    var _u_descriptor_Seres.UDiskSrv_OTAM.UDiskSrvOtamCtrlType.UPGRADE_TASK_INFO: descriptor_Seres.UDiskSrv_OTAM.UDiskSrvOtamCtrlType.UPGRADE_TASK_INFO
        get(){
            __u?.let{
                if ((__u as descriptor_Seres.UDiskSrv_OTAM.UDiskSrvOtamCtrlType.UPGRADE_TASK_INFO)._d == com.seres.background.upgrade.idlGen.UDiskSrv_OTAM.UDiskSrvOtamCtrlType.UPGRADE_TASK_INFO){
                    return __u!! as descriptor_Seres.UDiskSrv_OTAM.UDiskSrvOtamCtrlType.UPGRADE_TASK_INFO
                }else{
                    throw IllegalArgumentException("Error: union _d not match")
                }
            }?: throw IllegalArgumentException("Error: _u is not set")
        }

        set(@Suppress("UNUSED_PARAMETER") value){
            throw IllegalArgumentException("Error: can not set value to _u")
        }

    var _u_descriptor_Seres.UDiskSrv_OTAM.UDiskSrvOtamCtrlType.UPGRADE_TASK_IS_MATCH: descriptor_Seres.UDiskSrv_OTAM.UDiskSrvOtamCtrlType.UPGRADE_TASK_IS_MATCH
        get(){
            __u?.let{
                if ((__u as descriptor_Seres.UDiskSrv_OTAM.UDiskSrvOtamCtrlType.UPGRADE_TASK_IS_MATCH)._d == com.seres.background.upgrade.idlGen.UDiskSrv_OTAM.UDiskSrvOtamCtrlType.UPGRADE_TASK_IS_MATCH){
                    return __u!! as descriptor_Seres.UDiskSrv_OTAM.UDiskSrvOtamCtrlType.UPGRADE_TASK_IS_MATCH
                }else{
                    throw IllegalArgumentException("Error: union _d not match")
                }
            }?: throw IllegalArgumentException("Error: _u is not set")
        }

        set(@Suppress("UNUSED_PARAMETER") value){
            throw IllegalArgumentException("Error: can not set value to _u")
        }


}

