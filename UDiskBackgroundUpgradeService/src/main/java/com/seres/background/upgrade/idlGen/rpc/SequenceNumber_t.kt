package dds.rpc

import com.seres.dds.sdk.*
import com.seres.dds.sdk.idl.*


class SequenceNumber_t : TypeStruct(){
    private var high : Int = 0
    private var low : UInt = 0u

    init{
        typename = "dds::rpc::SequenceNumber_t"
        prop_create()
    }

    private fun prop_create(){
        super.prop = Prop("SequenceNumber_t", 0u, PropType.STRUCT, 0u)
        prop!!.create_member_prop(Prop("high", 1u, PropType.PRIMITIVE, 2u,"Int"))
        prop!!.create_member_prop(Prop("low", 2u, PropType.PRIMITIVE, 2u,"UInt"))
    }

    override fun serialize(buffer: Buffer){
        prop!!.members_prop.forEach{subprop->
            if(subprop.m_id == 1.toUInt()){
                subprop.machine!!.serialize(buffer, high)
            }
               if(subprop.m_id == 2.toUInt()){
                subprop.machine!!.serialize(buffer, low)
            }
        }
    }

    override fun deserialize(buffer: Buffer): SequenceNumber_t{
        prop!!.members_prop.forEach{subprop->
            if(subprop.m_id == 1.toUInt()){
                var res = subprop.machine!!.deserialize(buffer)
                this.high = res as Int
            }
            if(subprop.m_id == 2.toUInt()){
                var res = subprop.machine!!.deserialize(buffer)
                this.low = res as UInt
            }
        }
        return this
    }

    fun copy(value: SequenceNumber_t){
        high = value.high
        low = value.low
    }

    fun get_high(): Int{
        return high
    }

    fun set_high(value: Int){
        high = value
    }

    fun get_low(): UInt{
        return low
    }

    fun set_low(value: UInt){
        low = value
    }
}