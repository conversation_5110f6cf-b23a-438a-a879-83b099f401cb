package com.seres.background.upgrade.receiver

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.hardware.usb.UsbDevice
import android.hardware.usb.UsbManager
import com.seres.background.upgrade.service.UDiskBackgroundUpgradeService
import com.seres.background.upgrade.service.UsbDetectionService
import com.seres.background.upgrade.utils.LogUtils

/**
 * USB设备插拔接收器
 * 监听USB设备的插入和拔出事件
 */
class UsbDeviceReceiver : BroadcastReceiver() {
    
    private val TAG = "UsbDeviceReceiver"
    
    override fun onReceive(context: Context, intent: Intent) {
        when (intent.action) {
            UsbManager.ACTION_USB_DEVICE_ATTACHED -> {
                val device = intent.getParcelableExtra<UsbDevice>(UsbManager.EXTRA_DEVICE)
                device?.let {
                    LogUtils.i(TAG, "USB设备插入: ${it.deviceName}")

                    val serviceIntent = Intent(context, UDiskBackgroundUpgradeService::class.java)
                    context.startForegroundService(serviceIntent)
                    LogUtils.i(TAG, "后台升级服务启动成功")

                    notifyUsbDetectionService(context, UsbDetectionService.ACTION_USB_ATTACHED, it.deviceName)
                }
            }
            
            UsbManager.ACTION_USB_DEVICE_DETACHED -> {
                val device = intent.getParcelableExtra<UsbDevice>(UsbManager.EXTRA_DEVICE)
                device?.let {
                    LogUtils.i(TAG, "USB设备拔出: ${it.deviceName}")
                    notifyUsbDetectionService(context, UsbDetectionService.ACTION_USB_DETACHED, it.deviceName)
                }
            }
            
            Intent.ACTION_MEDIA_MOUNTED -> {
                val path = intent.data?.path
                path?.let {
                    LogUtils.i(TAG, "存储设备挂载: $it")
                    notifyUsbDetectionService(context, UsbDetectionService.ACTION_MEDIA_MOUNTED, it)
                }
            }
            
            Intent.ACTION_MEDIA_UNMOUNTED,
            Intent.ACTION_MEDIA_EJECT -> {
                val path = intent.data?.path
                path?.let {
                    LogUtils.i(TAG, "存储设备卸载: $it")
                    notifyUsbDetectionService(context, UsbDetectionService.ACTION_MEDIA_UNMOUNTED, it)
                }
            }
        }
    }
    
    /**
     * 通知USB检测服务
     */
    private fun notifyUsbDetectionService(context: Context, action: String, path: String) {
        try {
            val serviceIntent = Intent(context, UsbDetectionService::class.java).apply {
                this.action = action
                putExtra(UsbDetectionService.EXTRA_DEVICE_PATH, path)
            }
            context.startForegroundService(serviceIntent)
        } catch (e: Exception) {
            LogUtils.e(TAG, "通知USB检测服务失败: ${e.message}")
        }
    }
}
