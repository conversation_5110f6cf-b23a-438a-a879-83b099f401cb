package com.seres.background.upgrade

import android.app.Application
import android.content.Intent
import com.seres.background.upgrade.service.UDiskBackgroundUpgradeService
import com.seres.background.upgrade.utils.LogUtils

/**
 * 后台升级服务应用程序类
 * 负责应用程序的初始化和全局配置
 */
class BackgroundUpgradeApplication : Application() {

    override fun onCreate() {
        super.onCreate()
        LogUtils.d(TAG, "BackgroundUpgradeApplication onCreate")
        
        // 启动后台升级服务
        startUDiskBackgroundUpgradeService()
    }
    
    /**
     * 启动后台升级服务
     */
    private fun startUDiskBackgroundUpgradeService() {
        try {
            val intent = Intent(this, UDiskBackgroundUpgradeService::class.java)
            intent.action = "com.seres.background.upgrade.START_SERVICE"

            // 尝试启动前台服务
            try {
                startForegroundService(intent)
                LogUtils.d(TAG, "后台升级服务启动成功 (前台服务)")
            } catch (e: Exception) {
                // 如果前台服务启动失败，尝试普通服务
                LogUtils.w(TAG, "前台服务启动失败，尝试普通服务: ${e.message}")
                startService(intent)
                LogUtils.d(TAG, "后台升级服务启动成功 (普通服务)")
            }
        } catch (e: Exception) {
            LogUtils.e(TAG, "启动后台升级服务失败", e)
        }
    }

    companion object {
        private const val TAG = "BackgroundUpgradeApplication"
    }
}
