package dds.rpc

import com.seres.dds.sdk.TypeStruct
import com.seres.dds.sdk.idl.*


class SampleIdentity : TypeStruct(){
    private var writer_guid : GUID_t = GUID_t()
    private var sequence_number : SequenceNumber_t = SequenceNumber_t()

    init{
        typename = "dds::rpc::SampleIdentity"
        prop_create()
    }

    private fun prop_create(){
        super.prop = Prop("SampleIdentity", 0u, PropType.STRUCT, 0u)
        prop!!.create_member_prop(Prop("writer_guid", 1u, PropType.STRUCT, 4u))
        prop!!.create_member_prop(Prop("sequence_number", 2u, PropType.STRUCT, 4u))
    }

    override fun serialize(buffer: Buffer){
        prop!!.members_prop.forEach{subprop->
            if(subprop.m_id == 1.toUInt()){
                writer_guid.serialize(buffer)
            }
               if(subprop.m_id == 2.toUInt()){
                sequence_number.serialize(buffer)
            }
        }
    }

    override fun deserialize(buffer: Buffer): SampleIdentity{
        prop!!.members_prop.forEach{subprop->
            if(subprop.m_id == 1.toUInt()){
//                var res = subprop.machine!!.deserialize(buffer)
                this.writer_guid = writer_guid.deserialize(buffer)
              }
            if(subprop.m_id == 2.toUInt()){
//                var res = subprop.machine!!.deserialize(buffer)
                this.sequence_number = sequence_number.deserialize(buffer)
              }
        }
        return this
    }

    fun copy(value: SampleIdentity):SampleIdentity{
        writer_guid.copy(value.writer_guid)
        sequence_number.copy(value.sequence_number)
        return this
    }

    fun get_writer_guid(): GUID_t{

        return writer_guid
    }

    fun set_writer_guid(value: GUID_t){
        writer_guid.copy(value)
    }

    fun get_sequence_number(): SequenceNumber_t{
        return this.sequence_number
    }

    fun set_sequence_number(value: SequenceNumber_t){
        sequence_number.copy(value)
    }
}