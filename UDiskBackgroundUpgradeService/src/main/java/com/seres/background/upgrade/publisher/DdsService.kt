package com.seres.background.upgrade.publisher

import com.seres.dds.sdk.*
import Seres.UDiskSrv_OTAM.UDiskSrv_OTAM_Control
import Seres.OTAM_UDiskSrv.OTAM_UDiskSrv_Status
import com.seres.background.upgrade.utils.LogUtils
import com.google.gson.Gson
import java.util.concurrent.Executors
import java.util.concurrent.ScheduledExecutorService
import java.util.concurrent.TimeUnit

/**
 * DDS服务类
 * 负责使用DDS库实现S2S任务发布和资产信息订阅
 */
class DdsService {

    private val TAG = "DdsService"
    private val gson = Gson()
    private val executor: ScheduledExecutorService = Executors.newScheduledThreadPool(2)

    // DDS实体
    private var domainParticipant: DomainParticipant? = null

    // 发布者相关
    private var publisher: Publisher? = null
    private var upgradeTaskTopic: Topic? = null
    private var upgradeTaskWriter: DataWriter? = null

    // 订阅者相关
    private var subscriber: Subscriber? = null
    private var inventoryTopic: Topic? = null
    private var inventoryReader: DataReader? = null

    // 回调接口
    private var inventoryInfoCallback: InventoryInfoCallback? = null

    /**
     * 资产信息回调接口
     */
    interface InventoryInfoCallback {
        fun onInventoryInfoReceived(inventoryInfoStatus: String)
    }

    /**
     * 初始化DDS服务
     */
    fun initialize(): Boolean {
        return try {
            LogUtils.i(TAG, "初始化DDS服务")

            // 创建DomainParticipant
            domainParticipant = DomainParticipant(0)
            LogUtils.i(TAG, "DomainParticipant创建成功")

            // 初始化发布者
            initializePublisher()

            // 初始化订阅者
            initializeSubscriber()

            // 启动数据读取任务
            startInventoryDataReading()

            LogUtils.i(TAG, "DDS服务初始化完成")
            true
        } catch (e: Exception) {
            LogUtils.e(TAG, "DDS服务初始化失败: ${e.message}", e)
            false
        }
    }

    /**
     * 初始化发布者
     */
    private fun initializePublisher() {
        // 创建Publisher
        publisher = Publisher(domainParticipant!!)
        LogUtils.i(TAG, "Publisher创建成功")

        // 创建升级任务Topic和DataWriter
        upgradeTaskTopic = Topic(domainParticipant!!, "UDiskSrv_OTAM_Control", UDiskSrv_OTAM_Control())
        upgradeTaskWriter = DataWriter(publisher!!, upgradeTaskTopic!!)
        LogUtils.i(TAG, "升级任务Topic和DataWriter创建成功")
    }

    /**
     * 初始化订阅者
     */
    private fun initializeSubscriber() {
        // 创建Subscriber
        subscriber = Subscriber(domainParticipant!!)
        LogUtils.i(TAG, "Subscriber创建成功")

        // 创建资产信息Topic和DataReader
        inventoryTopic = Topic(domainParticipant!!, "OTAM_UDiskSrv_Status", OTAM_UDiskSrv_Status())
        inventoryReader = DataReader(subscriber!!, inventoryTopic!!)
        LogUtils.i(TAG, "资产信息Topic和DataReader创建成功")
    }

    /**
     * 启动资产信息数据读取任务
     */
    private fun startInventoryDataReading() {
        executor.scheduleWithFixedDelay({
            try {
                readInventoryData()
            } catch (e: Exception) {
                LogUtils.e(TAG, "读取资产信息数据时出错: ${e.message}", e)
            }
        }, 1, 1, TimeUnit.SECONDS) // 每秒检查一次
    }

    /**
     * 读取资产信息数据
     */
    private fun readInventoryData() {
        inventoryReader?.let { reader ->
            val samples = reader.take(10) // 一次最多读取10个样本

            if (samples.sample_count > 0) {
                LogUtils.d(TAG, "收到 ${samples.sample_count} 个资产信息样本")

                samples.sample_list?.forEach { sample ->
                    sample.info?.let { info ->
                        if (info.valid_data) {
                            val inventoryStatus = sample.type as OTAM_UDiskSrv_Status
                            val inventoryInfoStatus = inventoryStatus.inventory_info_status

                            LogUtils.d(TAG, "收到资产信息: $inventoryInfoStatus")

                            // 通过回调通知上层
                            inventoryInfoStatus?.let { status ->
                                inventoryInfoCallback?.onInventoryInfoReceived(status)
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * 发布升级任务信息
     */
    fun publishUpgradeTaskInfo(taskJson: String): Boolean {
        return try {
            LogUtils.i(TAG, "发布升级任务信息")

            // 创建UpgradeTaskInfoDescriptor实例
            val upgradeTaskInfo = UDiskSrv_OTAM_Control()
            upgradeTaskInfo._u_descriptor_1892738123.upgrade_task_info = taskJson

            // 发布数据
            val result = upgradeTaskWriter?.write(upgradeTaskInfo) ?: -1

            if (result == 0) {
                LogUtils.i(TAG, "升级任务信息发布成功")
                true
            } else {
                LogUtils.e(TAG, "升级任务信息发布失败，错误码: $result")
                false
            }
        } catch (e: Exception) {
            LogUtils.e(TAG, "发布升级任务信息时出错: ${e.message}", e)
            false
        }
    }

    /**
     * 发布升级任务匹配结果
     */
    fun publishUpgradeTaskMatch(isMatch: Boolean): Boolean {
        return try {
            LogUtils.i(TAG, "发布升级任务匹配结果: $isMatch")

            // 创建UpgradeTaskIsMatchDescriptor实例
            val upgradeTaskMatch = UDiskSrv_OTAM_Control()
            upgradeTaskMatch._u_descriptor_374875982.upgrade_task_is_match = isMatch

            // 发布数据
            val result = upgradeTaskWriter?.write(upgradeTaskMatch) ?: -1

            if (result == 0) {
                LogUtils.i(TAG, "升级任务匹配结果发布成功")
                true
            } else {
                LogUtils.e(TAG, "升级任务匹配结果发布失败，错误码: $result")
                false
            }
        } catch (e: Exception) {
            LogUtils.e(TAG, "发布升级任务匹配结果时出错: ${e.message}", e)
            false
        }
    }

    /**
     * 设置资产信息回调
     */
    fun setInventoryInfoCallback(callback: InventoryInfoCallback) {
        this.inventoryInfoCallback = callback
    }

    /**
     * 检查DDS连接状态
     */
    fun isConnected(): Boolean {
        return domainParticipant != null &&
               publisher != null &&
               upgradeTaskWriter != null &&
               subscriber != null &&
               inventoryReader != null
    }

    /**
     * 清理资源
     */
    fun cleanup() {
        try {
            LogUtils.i(TAG, "清理DDS资源")

            // 停止定时任务
            executor.shutdownNow()

            // 清理DDS实体
            // 注意：DDS实体的销毁顺序很重要，应该按照创建的相反顺序销毁

            // 清理DataReader
            inventoryReader = null

            // 清理DataWriter
            upgradeTaskWriter = null

            // 清理Topic
            inventoryTopic = null
            upgradeTaskTopic = null

            // 清理Publisher和Subscriber
            publisher = null
            subscriber = null

            // 清理DomainParticipant
            domainParticipant = null

            LogUtils.i(TAG, "DDS资源清理完成")
        } catch (e: Exception) {
            LogUtils.e(TAG, "清理DDS资源时出错: ${e.message}", e)
        }
    }
}