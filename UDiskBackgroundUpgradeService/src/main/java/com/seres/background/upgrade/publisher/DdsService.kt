package com.seres.background.upgrade.publisher

import com.seres.dds.sdk.*
import com.seres.background.upgrade.idlGen.UDiskSrv_OTAM.UDiskSrv_OTAM_Control
class DdsService {

    private var domainParticipant: DomainParticipant? = null
    private var publisher: Publisher? = null
    private var topic: Topic? = null
    private var dataWriter: DataWriter? = null

    fun initialize() {
        // 初始化DDS
        domainParticipant = DomainParticipant(0)
        publisher = Publisher(domainParticipant!!)
        topic = Topic(domainParticipant!!, "UDiskSrv_OTAM_Control", UDiskSrv_OTAM_Control())
        dataWriter = DataWriter(publisher!!, topic!!)
    }
    fun publishData(data: UDiskSrv_OTAM_Control.UpgradeTaskIsMatchDescriptor) {
        dataWriter?.write(data)
    }

}